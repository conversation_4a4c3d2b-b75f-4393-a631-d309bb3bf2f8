#!/usr/bin/env python3
"""
高级Optuna超参数优化脚本
用于同时优化TimeXer模型的d_model、d_ff和patch_len参数
支持多个预测长度的参数调优，包含进度展示和可视化结果
"""

import os
import sys
import subprocess
import argparse
import optuna
import json
import re
import time
import numpy as np
import matplotlib.pyplot as plt
# 尝试导入seaborn，如果失败则使用matplotlib替代
try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False
    print("Seaborn未安装，将使用matplotlib替代热力图功能")
import pandas as pd
from typing import Dict, Any, Tuple, List
import logging
from datetime import datetime
from tqdm import tqdm

# 尝试导入plotly，如果失败则使用matplotlib
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    print("Plotly未安装，将使用matplotlib进行可视化")

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ProgressCallback:
    """进度回调类，用于显示优化进度"""
    
    def __init__(self, n_trials: int):
        self.n_trials = n_trials
        self.pbar = None
        self.best_value = float('inf')
        self.trial_count = 0
        
    def __call__(self, study: optuna.Study, trial: optuna.Trial):
        if self.pbar is None:
            self.pbar = tqdm(total=self.n_trials, desc="HPO Progress")
        
        self.trial_count += 1
        current_value = trial.value if trial.value is not None else float('inf')
        
        if current_value < self.best_value:
            self.best_value = current_value
            self.pbar.set_postfix({
                'Best RMSE': f'{self.best_value:.6f}',
                'Current': f'{current_value:.6f}',
                'Trial': f'{self.trial_count}/{self.n_trials}'
            })
        
        self.pbar.update(1)
        
        if self.trial_count >= self.n_trials:
            self.pbar.close()


class TimeXerOptunaOptimizer:
    def __init__(self, 
                 dataset: str,
                 pred_lens: List[int] = [6, 12, 24],
                 n_trials: int = 100,
                 study_name: str = None,
                 storage: str = None):
        """
        初始化Optuna优化器
        
        Args:
            dataset: 数据集名称
            pred_lens: 预测长度列表
            n_trials: 每个预测长度的试验次数
            study_name: Study名称
            storage: 数据库存储路径
        """
        self.dataset = dataset
        self.pred_lens = pred_lens
        self.n_trials = n_trials
        self.study_name = study_name or f"timexer_{dataset}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.storage = storage
        
        # guojia流域固定参数
        self.fixed_params = {
            'model': 'TimeXer',
            'seq_len': 168,
            'label_len': 96,
            'batch_size': 4,
            'e_layers': 1,
            'loss': 'RMSE',
            'features': 'MS',
            'itr': 1,
            'learning_rate': 0.0001,
            'en_seq_len': 168
        }
        
        # 搜索空间（根据用户需求定制）
        self.search_space = {
            'd_model': [16, 32, 64, 128, 256, 512, 1024],
            'd_ff': [128, 256, 512, 1024, 2048],
            'patch_len': [4, 8, 16]
        }
        
        # 创建结果目录
        self.results_dir = f"optuna_results/{self.study_name}"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 存储所有结果
        self.all_results = {}
        
    def define_search_space(self, trial: optuna.Trial) -> Dict[str, Any]:
        """定义搜索空间"""
        d_model = trial.suggest_categorical('d_model', self.search_space['d_model'])
        d_ff = trial.suggest_categorical('d_ff', self.search_space['d_ff'])
        patch_len = trial.suggest_categorical('patch_len', self.search_space['patch_len'])
        
        # 确保patch_len能整除seq_len
CondaError: Run 'conda init' before 'conda deactivate'
        
        return {
            'd_model': d_model,
            'd_ff': d_ff,
            'patch_len': patch_len
        }
    
    def run_experiment(self, params: Dict[str, Any], pred_len: int) -> float:
        """运行单次实验"""
        # 构建命令
        cmd = [
            'python', '-u', 'run_longExp.py',
            '--dataset', self.dataset,
            '--model', self.fixed_params['model'],
            '--seq_len', str(self.fixed_params['seq_len']),
            '--label_len', str(self.fixed_params['label_len']),
            '--pred_len', str(pred_len),
            '--batch_size', str(self.fixed_params['batch_size']),
            '--e_layers', str(self.fixed_params['e_layers']),
            '--d_model', str(params['d_model']),
            '--d_ff', str(params['d_ff']),
            '--patch_len', str(params['patch_len']),
            '--loss', self.fixed_params['loss'],
            '--features', self.fixed_params['features'],
            '--itr', str(self.fixed_params['itr']),
            '--learning_rate', str(self.fixed_params['learning_rate']),
            '--en_seq_len', str(self.fixed_params['en_seq_len']),
            '--ex_seq_len', str(self.fixed_params['en_seq_len'] + pred_len)
        ]
        
        logger.info(f"运行实验 - pred_len: {pred_len}, 参数: {params}")
        
        try:
            # 激活conda环境并运行实验
            conda_cmd = [
                'bash', '-c',
                'conda deactivate && conda deactivate && conda activate LIFT && ' + ' '.join(cmd)
            ]
            
            result = subprocess.run(
                conda_cmd,
                capture_output=True,
                text=True,
                timeout=3600,  # 1小时超时
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            
            if result.returncode != 0:
                logger.error(f"实验失败: {result.stderr}")
                return float('inf')
            
            # 解析RMSE
            rmse = self.parse_rmse_from_output(result.stdout)
            logger.info(f"实验完成 - RMSE: {rmse:.6f}")
            
            return rmse
            
        except subprocess.TimeoutExpired:
            logger.error("实验超时")
            return float('inf')
        except Exception as e:
            logger.error(f"实验异常: {e}")
            return float('inf')
    
    def parse_rmse_from_output(self, output: str) -> float:
        """从输出中解析RMSE值"""
        lines = output.split('\n')
        
        # 查找包含RMSE的行
        for line in lines:
            if 'rmse' in line.lower() and '[' in line and ']' in line:
                match = re.search(r'rmse.*?(\d+\.?\d*)', line.lower())
                if match:
                    return float(match.group(1))
        
        # 备用解析方法
        for line in lines:
            if 'test' in line.lower() and 'rmse' in line.lower():
                numbers = re.findall(r'\d+\.?\d*', line)
                if numbers:
                    return float(numbers[0])
        
        logger.warning("无法解析RMSE，返回默认值")
        return 999.0
    
    def create_objective_function(self, pred_len: int):
        """创建目标函数"""
        def objective(trial: optuna.Trial) -> float:
            params = self.define_search_space(trial)
            rmse = self.run_experiment(params, pred_len)
            
            # 记录到trial的用户属性中
            trial.set_user_attr('d_model', params['d_model'])
            trial.set_user_attr('d_ff', params['d_ff'])
            trial.set_user_attr('patch_len', params['patch_len'])
            trial.set_user_attr('pred_len', pred_len)
            trial.set_user_attr('rmse', rmse)
            
            return rmse
        
        return objective
    
    def optimize_for_pred_len(self, pred_len: int) -> optuna.Study:
        """为特定预测长度进行优化"""
        study_name_full = f"{self.study_name}_pred{pred_len}"
        
        # 创建study
        if self.storage:
            study = optuna.create_study(
                direction='minimize',
                study_name=study_name_full,
                storage=self.storage,
                load_if_exists=True
            )
        else:
            study = optuna.create_study(direction='minimize')
        
        logger.info(f"开始优化 pred_len={pred_len}，共{self.n_trials}次试验")
        
        # 创建进度回调
        progress_callback = ProgressCallback(self.n_trials)
        
        # 执行优化
        study.optimize(
            self.create_objective_function(pred_len),
            n_trials=self.n_trials,
            callbacks=[progress_callback]
        )
        
        # 保存结果
        self.all_results[pred_len] = {
            'study': study,
            'best_trial': study.best_trial,
            'best_value': study.best_value,
            'best_params': study.best_params
        }
        
        logger.info(f"pred_len={pred_len} 优化完成!")
        logger.info(f"最佳RMSE: {study.best_value:.6f}")
        logger.info(f"最佳参数: {study.best_params}")
        
        return study

    def optimize_all(self):
        """对所有预测长度进行优化"""
        print(f"\n{'='*60}")
        print(f"开始TimeXer模型超参数优化")
        print(f"数据集: {self.dataset}")
        print(f"预测长度: {self.pred_lens}")
        print(f"每个长度试验次数: {self.n_trials}")
        print(f"搜索空间:")
        print(f"  d_model: {self.search_space['d_model']}")
        print(f"  d_ff: {self.search_space['d_ff']}")
        print(f"  patch_len: {self.search_space['patch_len']}")
        print(f"{'='*60}\n")

        for pred_len in self.pred_lens:
            print(f"\n🚀 开始优化 pred_len = {pred_len}")
            study = self.optimize_for_pred_len(pred_len)

            # 保存单个预测长度的结果
            self.save_single_result(pred_len, study)

        # 生成综合报告和可视化
        self.generate_comprehensive_report()

        return self.all_results

    def save_single_result(self, pred_len: int, study: optuna.Study):
        """保存单个预测长度的结果"""
        result_file = f"{self.results_dir}/pred_len_{pred_len}_result.json"

        result = {
            'pred_len': pred_len,
            'best_rmse': study.best_value,
            'best_params': study.best_params,
            'n_trials': len(study.trials),
            'study_name': study.study_name if hasattr(study, 'study_name') else None,
            'timestamp': datetime.now().isoformat()
        }

        with open(result_file, 'w') as f:
            json.dump(result, f, indent=2)

        logger.info(f"结果已保存到: {result_file}")

    def generate_comprehensive_report(self):
        """生成综合报告和可视化"""
        print(f"\n📊 生成综合分析报告...")

        # 创建分析目录
        analysis_dir = f"{self.results_dir}/analysis"
        os.makedirs(analysis_dir, exist_ok=True)

        # 生成各种可视化
        self.create_summary_table()
        self.create_parameter_importance_plot()
        self.create_optimization_history_plot()
        self.create_parameter_distribution_plot()
        self.create_interactive_dashboard()

        # 保存综合结果
        self.save_comprehensive_results()

        print(f"📈 所有可视化结果已保存到: {analysis_dir}")

    def create_summary_table(self):
        """创建结果汇总表"""
        summary_data = []

        for pred_len, result in self.all_results.items():
            summary_data.append({
                'pred_len': pred_len,
                'best_rmse': result['best_value'],
                'd_model': result['best_params']['d_model'],
                'd_ff': result['best_params']['d_ff'],
                'patch_len': result['best_params']['patch_len'],
                'n_trials': len(result['study'].trials)
            })

        df = pd.DataFrame(summary_data)

        # 保存CSV
        csv_file = f"{self.results_dir}/analysis/summary_results.csv"
        df.to_csv(csv_file, index=False)

        # 创建美观的表格图
        fig, ax = plt.subplots(figsize=(12, 6))
        ax.axis('tight')
        ax.axis('off')

        table = ax.table(cellText=df.values,
                        colLabels=df.columns,
                        cellLoc='center',
                        loc='center')

        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)

        # 设置表格样式
        for i in range(len(df.columns)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')

        plt.title(f'{self.dataset}数据集 - TimeXer模型超参数优化结果汇总',
                 fontsize=14, fontweight='bold', pad=20)
        plt.savefig(f"{self.results_dir}/analysis/summary_table.png",
                   dpi=300, bbox_inches='tight')
        plt.close()

        print("✅ 结果汇总表已生成")

    def create_parameter_importance_plot(self):
        """创建参数重要性图"""
        fig, axes = plt.subplots(1, len(self.pred_lens), figsize=(5*len(self.pred_lens), 6))
        if len(self.pred_lens) == 1:
            axes = [axes]

        for idx, (pred_len, result) in enumerate(self.all_results.items()):
            study = result['study']

            try:
                # 计算参数重要性
                importance = optuna.importance.get_param_importances(study)

                params = list(importance.keys())
                values = list(importance.values())

                # 创建条形图
                bars = axes[idx].bar(params, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
                axes[idx].set_title(f'参数重要性 (pred_len={pred_len})', fontweight='bold')
                axes[idx].set_ylabel('重要性分数')
                axes[idx].tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    axes[idx].text(bar.get_x() + bar.get_width()/2., height,
                                 f'{value:.3f}', ha='center', va='bottom')

            except Exception as e:
                axes[idx].text(0.5, 0.5, f'无法计算参数重要性\n{str(e)}',
                             ha='center', va='center', transform=axes[idx].transAxes)
                axes[idx].set_title(f'参数重要性 (pred_len={pred_len})', fontweight='bold')

        plt.tight_layout()
        plt.savefig(f"{self.results_dir}/analysis/parameter_importance.png",
                   dpi=300, bbox_inches='tight')
        plt.close()

        print("✅ 参数重要性图已生成")

    def create_optimization_history_plot(self):
        """创建优化历史图"""
        fig, axes = plt.subplots(1, len(self.pred_lens), figsize=(6*len(self.pred_lens), 5))
        if len(self.pred_lens) == 1:
            axes = [axes]

        for idx, (pred_len, result) in enumerate(self.all_results.items()):
            study = result['study']

            # 获取试验历史
            trials = study.trials
            trial_numbers = [t.number for t in trials if t.value is not None]
            values = [t.value for t in trials if t.value is not None]

            if not values:
                continue

            # 计算累积最佳值
            best_values = []
            current_best = float('inf')
            for value in values:
                if value < current_best:
                    current_best = value
                best_values.append(current_best)

            # 绘制优化历史
            axes[idx].plot(trial_numbers, values, 'o-', alpha=0.6, label='试验结果', markersize=4)
            axes[idx].plot(trial_numbers, best_values, 'r-', linewidth=2, label='累积最佳')

            axes[idx].set_xlabel('试验次数')
            axes[idx].set_ylabel('RMSE')
            axes[idx].set_title(f'优化历史 (pred_len={pred_len})', fontweight='bold')
            axes[idx].legend()
            axes[idx].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.results_dir}/analysis/optimization_history.png",
                   dpi=300, bbox_inches='tight')
        plt.close()

        print("✅ 优化历史图已生成")

    def create_parameter_distribution_plot(self):
        """创建参数分布图"""
        # 收集所有试验数据
        all_data = []

        for pred_len, result in self.all_results.items():
            study = result['study']
            for trial in study.trials:
                if trial.value is not None:
                    all_data.append({
                        'pred_len': pred_len,
                        'rmse': trial.value,
                        'd_model': trial.params.get('d_model'),
                        'd_ff': trial.params.get('d_ff'),
                        'patch_len': trial.params.get('patch_len'),
                        'trial_number': trial.number
                    })

        if not all_data:
            return

        df = pd.DataFrame(all_data)

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. RMSE分布直方图
        for pred_len in self.pred_lens:
            pred_data = df[df['pred_len'] == pred_len]['rmse']
            axes[0, 0].hist(pred_data, alpha=0.7, label=f'pred_len={pred_len}', bins=20)
        axes[0, 0].set_xlabel('RMSE')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].set_title('RMSE分布', fontweight='bold')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. d_model vs RMSE
        for pred_len in self.pred_lens:
            pred_data = df[df['pred_len'] == pred_len]
            axes[0, 1].scatter(pred_data['d_model'], pred_data['rmse'],
                             alpha=0.6, label=f'pred_len={pred_len}', s=30)
        axes[0, 1].set_xlabel('d_model')
        axes[0, 1].set_ylabel('RMSE')
        axes[0, 1].set_title('d_model vs RMSE', fontweight='bold')
        axes[0, 1].set_xscale('log', base=2)
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 3. d_ff vs RMSE
        for pred_len in self.pred_lens:
            pred_data = df[df['pred_len'] == pred_len]
            axes[1, 0].scatter(pred_data['d_ff'], pred_data['rmse'],
                             alpha=0.6, label=f'pred_len={pred_len}', s=30)
        axes[1, 0].set_xlabel('d_ff')
        axes[1, 0].set_ylabel('RMSE')
        axes[1, 0].set_title('d_ff vs RMSE', fontweight='bold')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 4. patch_len vs RMSE
        for pred_len in self.pred_lens:
            pred_data = df[df['pred_len'] == pred_len]
            axes[1, 1].scatter(pred_data['patch_len'], pred_data['rmse'],
                             alpha=0.6, label=f'pred_len={pred_len}', s=30)
        axes[1, 1].set_xlabel('patch_len')
        axes[1, 1].set_ylabel('RMSE')
        axes[1, 1].set_title('patch_len vs RMSE', fontweight='bold')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.results_dir}/analysis/parameter_distributions.png",
                   dpi=300, bbox_inches='tight')
        plt.close()

        # 创建热力图
        self.create_heatmap(df)

        print("✅ 参数分布图已生成")

    def create_heatmap(self, df: pd.DataFrame):
        """创建参数组合热力图"""
        fig, axes = plt.subplots(1, len(self.pred_lens), figsize=(6*len(self.pred_lens), 5))
        if len(self.pred_lens) == 1:
            axes = [axes]

        for idx, pred_len in enumerate(self.pred_lens):
            pred_data = df[df['pred_len'] == pred_len]

            # 创建d_model vs patch_len的热力图
            pivot_table = pred_data.groupby(['d_model', 'patch_len'])['rmse'].mean().reset_index()
            pivot_matrix = pivot_table.pivot(index='d_model', columns='patch_len', values='rmse')

            if SEABORN_AVAILABLE:
                sns.heatmap(pivot_matrix, annot=True, fmt='.4f', cmap='viridis_r',
                           ax=axes[idx], cbar_kws={'label': 'Average RMSE'})
            else:
                # 使用matplotlib替代热力图
                im = axes[idx].imshow(pivot_matrix.values, cmap='viridis_r', aspect='auto')
                axes[idx].set_xticks(range(len(pivot_matrix.columns)))
                axes[idx].set_yticks(range(len(pivot_matrix.index)))
                axes[idx].set_xticklabels(pivot_matrix.columns)
                axes[idx].set_yticklabels(pivot_matrix.index)

                # 添加数值标注
                for i in range(len(pivot_matrix.index)):
                    for j in range(len(pivot_matrix.columns)):
                        value = pivot_matrix.iloc[i, j]
                        if not np.isnan(value):
                            axes[idx].text(j, i, f'{value:.4f}', ha='center', va='center',
                                         color='white' if value < pivot_matrix.values.mean() else 'black')

                # 添加颜色条
                plt.colorbar(im, ax=axes[idx], label='Average RMSE')
            axes[idx].set_title(f'd_model vs patch_len 热力图\n(pred_len={pred_len})', fontweight='bold')
            axes[idx].set_xlabel('patch_len')
            axes[idx].set_ylabel('d_model')

        plt.tight_layout()
        plt.savefig(f"{self.results_dir}/analysis/parameter_heatmap.png",
                   dpi=300, bbox_inches='tight')
        plt.close()

    def create_interactive_dashboard(self):
        """创建交互式仪表板（如果plotly可用）"""
        if not PLOTLY_AVAILABLE:
            print("⚠️ Plotly不可用，跳过交互式仪表板生成")
            return

        # 收集所有数据
        all_data = []
        for pred_len, result in self.all_results.items():
            study = result['study']
            for trial in study.trials:
                if trial.value is not None:
                    all_data.append({
                        'pred_len': pred_len,
                        'rmse': trial.value,
                        'd_model': trial.params.get('d_model'),
                        'd_ff': trial.params.get('d_ff'),
                        'patch_len': trial.params.get('patch_len'),
                        'trial_number': trial.number
                    })

        if not all_data:
            return

        df = pd.DataFrame(all_data)

        # 创建交互式散点图
        fig = px.scatter_3d(df, x='d_model', y='d_ff', z='patch_len',
                           color='rmse', size='rmse',
                           facet_col='pred_len',
                           title=f'{self.dataset}数据集 - 参数空间可视化',
                           labels={'rmse': 'RMSE', 'd_model': 'Model Dimension',
                                  'd_ff': 'Feed Forward Dimension', 'patch_len': 'Patch Length'})

        # 保存交互式图表
        pyo.plot(fig, filename=f"{self.results_dir}/analysis/interactive_dashboard.html", auto_open=False)

        print("✅ 交互式仪表板已生成")

    def save_comprehensive_results(self):
        """保存综合结果"""
        comprehensive_result = {
            'dataset': self.dataset,
            'pred_lens': self.pred_lens,
            'n_trials_per_pred_len': self.n_trials,
            'search_space': self.search_space,
            'optimization_timestamp': datetime.now().isoformat(),
            'results_by_pred_len': {}
        }

        for pred_len, result in self.all_results.items():
            comprehensive_result['results_by_pred_len'][str(pred_len)] = {
                'best_rmse': result['best_value'],
                'best_params': result['best_params'],
                'total_trials': len(result['study'].trials),
                'successful_trials': len([t for t in result['study'].trials if t.value is not None])
            }

        # 找到全局最佳结果
        best_overall = min(self.all_results.items(), key=lambda x: x[1]['best_value'])
        comprehensive_result['best_overall'] = {
            'pred_len': best_overall[0],
            'rmse': best_overall[1]['best_value'],
            'params': best_overall[1]['best_params']
        }

        with open(f"{self.results_dir}/comprehensive_results.json", 'w') as f:
            json.dump(comprehensive_result, f, indent=2)

        print("✅ 综合结果已保存")

        # 打印最终总结
        self.print_final_summary()

    def print_final_summary(self):
        """打印最终总结"""
        print(f"\n{'='*80}")
        print(f"🎉 TimeXer模型超参数优化完成!")
        print(f"{'='*80}")
        print(f"数据集: {self.dataset}")
        print(f"总试验次数: {len(self.pred_lens) * self.n_trials}")
        print(f"\n📊 各预测长度最佳结果:")
        print("-" * 80)

        for pred_len, result in sorted(self.all_results.items()):
            print(f"pred_len = {pred_len:2d} | RMSE: {result['best_value']:8.6f} | "
                  f"d_model: {result['best_params']['d_model']:4d} | "
                  f"d_ff: {result['best_params']['d_ff']:4d} | "
                  f"patch_len: {result['best_params']['patch_len']:2d}")

        # 找到全局最佳
        best_overall = min(self.all_results.items(), key=lambda x: x[1]['best_value'])
        print("-" * 80)
        print(f"🏆 全局最佳结果:")
        print(f"   预测长度: {best_overall[0]}")
        print(f"   RMSE: {best_overall[1]['best_value']:.6f}")
        print(f"   最佳参数: {best_overall[1]['best_params']}")

        print(f"\n📁 所有结果已保存到: {self.results_dir}")
        print(f"{'='*80}\n")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TimeXer高级超参数优化')
    parser.add_argument('--dataset', type=str, required=True,
                       choices=['guojia', 'lianghe', 'chengkou', 'yantang'],
                       help='数据集名称')
    parser.add_argument('--pred_lens', type=int, nargs='+', default=[6, 12, 24],
                       help='预测长度列表 (默认: 6 12 24)')
    parser.add_argument('--n_trials', type=int, default=100,
                       help='每个预测长度的试验次数 (默认: 100)')
    parser.add_argument('--study_name', type=str, help='Study名称')
    parser.add_argument('--storage', type=str, help='数据库存储路径 (例如: sqlite:///optuna.db)')
    parser.add_argument('--resume', action='store_true', help='是否恢复之前的优化')

    args = parser.parse_args()

    # 验证预测长度
    valid_pred_lens = [6, 12, 24]
    for pred_len in args.pred_lens:
        if pred_len not in valid_pred_lens:
            print(f"警告: pred_len={pred_len} 不在推荐范围 {valid_pred_lens} 内")

    # 创建优化器
    optimizer = TimeXerOptunaOptimizer(
        dataset=args.dataset,
        pred_lens=args.pred_lens,
        n_trials=args.n_trials,
        study_name=args.study_name,
        storage=args.storage
    )

    try:
        # 执行优化
        results = optimizer.optimize_all()

        print("\n🎊 恭喜！超参数优化成功完成！")
        print(f"📊 查看详细结果: {optimizer.results_dir}")
        print(f"📈 可视化文件位置: {optimizer.results_dir}/analysis/")

        return results

    except KeyboardInterrupt:
        print("\n⚠️ 优化被用户中断")
        print(f"📊 部分结果已保存到: {optimizer.results_dir}")
        return optimizer.all_results
    except Exception as e:
        print(f"\n❌ 优化过程中发生错误: {e}")
        print(f"📊 部分结果已保存到: {optimizer.results_dir}")
        raise


if __name__ == "__main__":
    # 设置matplotlib后端（避免GUI问题）
    import matplotlib
    matplotlib.use('Agg')

    main()
