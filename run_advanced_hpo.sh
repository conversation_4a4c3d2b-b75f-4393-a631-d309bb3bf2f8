#!/bin/bash

# TimeXer高级超参数优化启动脚本
# 支持conda环境切换和多预测长度优化

# 设置默认参数
DATASET="guojia"
PRED_LENS="6 12 24"
N_TRIALS=100
CONDA_ENV="LIFT"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --dataset)
            DATASET="$2"
            shift 2
            ;;
        --pred_lens)
            PRED_LENS="$2"
            shift 2
            ;;
        --n_trials)
            N_TRIALS="$2"
            shift 2
            ;;
        --conda_env)
            CONDA_ENV="$2"
            shift 2
            ;;
        --help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --dataset DATASET         数据集名称 (guojia, lianghe, chengkou, yantang)"
            echo "  --pred_lens \"LENS\"        预测长度列表，用空格分隔 (默认: \"6 12 24\")"
            echo "  --n_trials N_TRIALS       每个预测长度的试验次数 (默认: 100)"
            echo "  --conda_env ENV           conda环境名称 (默认: LIFT)"
            echo "  --help                    显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 --dataset guojia --pred_lens \"6 12 24\" --n_trials 50"
            echo "  $0 --dataset lianghe --pred_lens \"12\" --n_trials 200"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 验证数据集参数
if [[ ! "$DATASET" =~ ^(guojia|lianghe|chengkou|yantang)$ ]]; then
    echo "错误: 无效的数据集名称 '$DATASET'"
    echo "支持的数据集: guojia, lianghe, chengkou, yantang"
    exit 1
fi

echo "🚀 开始TimeXer高级超参数优化..."
echo "================================"
echo "数据集: $DATASET"
echo "预测长度: $PRED_LENS"
echo "每个长度试验次数: $N_TRIALS"
echo "Conda环境: $CONDA_ENV"
echo "================================"

# 检查conda是否可用
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: conda未找到，请确保conda已安装并在PATH中"
    exit 1
fi

# 检查conda环境是否存在
if ! conda env list | grep -q "^$CONDA_ENV "; then
    echo "❌ 错误: conda环境 '$CONDA_ENV' 不存在"
    echo "请先创建环境或使用 --conda_env 指定正确的环境名称"
    exit 1
fi

# 创建日志目录
mkdir -p logs/hpo

# 生成日志文件名
LOG_FILE="logs/hpo/advanced_hpo_${DATASET}_$(date +%Y%m%d_%H%M%S).log"

echo "📝 日志将保存到: $LOG_FILE"
echo ""

# 激活conda环境并运行优化
echo "🔄 激活conda环境: $CONDA_ENV"

# 构建命令
CMD="conda deactivate && conda deactivate && conda activate $CONDA_ENV && python optuna_hpo_advanced.py --dataset $DATASET --pred_lens $PRED_LENS --n_trials $N_TRIALS"

echo "🏃 执行命令: $CMD"
echo ""

# 执行优化
bash -c "$CMD" 2>&1 | tee "$LOG_FILE"

# 检查执行结果
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo ""
    echo "🎉 优化成功完成!"
    echo "📊 查看结果目录: optuna_results/"
    echo "📝 日志文件: $LOG_FILE"
else
    echo ""
    echo "❌ 优化过程中出现错误"
    echo "📝 请查看日志文件: $LOG_FILE"
    exit 1
fi
