# TimeXer 高级超参数优化工具

这是一个专为TimeXer模型设计的高级超参数优化工具，使用Optuna框架实现多参数、多策略的HPO，支持多个预测长度的同时优化，并提供丰富的可视化结果。

## ✨ 主要特性

- 🎯 **精确搜索空间**: 根据需求定制的搜索空间
  - `d_model`: [16, 32, 64, 128, 256, 512, 1024]
  - `d_ff`: [128, 256, 512, 1024, 2048]  
  - `patch_len`: [4, 8, 16]

- 🚀 **多预测长度优化**: 同时对pred_len=6、12、24进行参数调优

- 📊 **实时进度展示**: 使用tqdm显示优化进度和最佳结果

- 🎨 **丰富可视化**: 生成多种美观的分析图表
  - 结果汇总表
  - 参数重要性分析
  - 优化历史图
  - 参数分布图
  - 热力图
  - 交互式仪表板（可选）

- 🔧 **智能环境管理**: 自动处理conda环境切换

## 📦 安装依赖

### 基础依赖（必需）
```bash
pip install -r hpo_requirements_basic.txt
```

### 可选依赖（增强可视化）
```bash
pip install plotly>=5.0.0
```

## 🚀 快速开始

### 1. 快速测试
```bash
python test_hpo_quick.py
```

### 2. 使用启动脚本（推荐）
```bash
# 基本用法
./run_advanced_hpo.sh --dataset guojia

# 自定义参数
./run_advanced_hpo.sh --dataset guojia --pred_lens "6 12 24" --n_trials 100

# 指定conda环境
./run_advanced_hpo.sh --dataset lianghe --conda_env LIFT --n_trials 50
```

### 3. 直接运行Python脚本
```bash
# 基本用法
python optuna_hpo_advanced.py --dataset guojia

# 自定义预测长度和试验次数
python optuna_hpo_advanced.py --dataset guojia --pred_lens 6 12 24 --n_trials 100

# 使用数据库存储（持久化）
python optuna_hpo_advanced.py --dataset guojia --storage sqlite:///optuna_study.db
```

## 📋 参数说明

### 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--dataset` | str | 必需 | 数据集名称 (guojia, lianghe, chengkou, yantang) |
| `--pred_lens` | int[] | [6, 12, 24] | 预测长度列表 |
| `--n_trials` | int | 100 | 每个预测长度的试验次数 |
| `--study_name` | str | 自动生成 | Optuna study名称 |
| `--storage` | str | None | 数据库存储路径 |
| `--resume` | flag | False | 是否恢复之前的优化 |

### 搜索空间配置

当前搜索空间完全按照您的需求配置：

```python
search_space = {
    'd_model': [16, 32, 64, 128, 256, 512, 1024],
    'd_ff': [128, 256, 512, 1024, 2048],
    'patch_len': [4, 8, 16]  # 自动确保能整除seq_len=168
}
```

## 📊 输出结果

### 目录结构
```
optuna_results/
└── timexer_guojia_20241215_143022/
    ├── pred_len_6_result.json          # 单个预测长度结果
    ├── pred_len_12_result.json
    ├── pred_len_24_result.json
    ├── comprehensive_results.json       # 综合结果
    └── analysis/                        # 可视化分析
        ├── summary_table.png           # 结果汇总表
        ├── summary_results.csv         # CSV格式结果
        ├── parameter_importance.png    # 参数重要性
        ├── optimization_history.png    # 优化历史
        ├── parameter_distributions.png # 参数分布
        ├── parameter_heatmap.png       # 热力图
        └── interactive_dashboard.html  # 交互式仪表板
```

### 实时进度显示
```
HPO Progress: 45%|████▌     | 45/100 [12:34<15:23, Best RMSE: 0.123456, Current: 0.234567, Trial: 45/100]
```

### 最终结果展示
```
================================================================================
🎉 TimeXer模型超参数优化完成!
================================================================================
数据集: guojia
总试验次数: 300

📊 各预测长度最佳结果:
--------------------------------------------------------------------------------
pred_len =  6 | RMSE: 0.123456 | d_model:  256 | d_ff:  512 | patch_len:  8
pred_len = 12 | RMSE: 0.145678 | d_model:  512 | d_ff: 1024 | patch_len:  4
pred_len = 24 | RMSE: 0.167890 | d_model:  256 | d_ff:  512 | patch_len: 16
--------------------------------------------------------------------------------
🏆 全局最佳结果:
   预测长度: 6
   RMSE: 0.123456
   最佳参数: {'d_model': 256, 'd_ff': 512, 'patch_len': 8}
================================================================================
```

## 🎨 可视化功能

### 1. 结果汇总表
- 清晰展示所有预测长度的最佳结果
- 包含RMSE值和对应的最佳参数组合

### 2. 参数重要性分析
- 使用Optuna的参数重要性算法
- 分析各参数对模型性能的影响程度

### 3. 优化历史图
- 显示每次试验的RMSE值
- 展示累积最佳值的变化趋势

### 4. 参数分布图
- RMSE分布直方图
- 各参数与RMSE的散点图关系

### 5. 热力图
- d_model vs patch_len的平均RMSE热力图
- 直观显示参数组合的性能

### 6. 交互式仪表板（需要plotly）
- 3D参数空间可视化
- 支持交互式探索

## 🔧 高级用法

### 1. 持久化存储
```bash
python optuna_hpo_advanced.py \
    --dataset guojia \
    --storage sqlite:///optuna_study.db \
    --study_name my_optimization
```

### 2. 恢复中断的优化
```bash
python optuna_hpo_advanced.py \
    --dataset guojia \
    --storage sqlite:///optuna_study.db \
    --study_name my_optimization \
    --resume
```

### 3. 自定义预测长度
```bash
python optuna_hpo_advanced.py \
    --dataset guojia \
    --pred_lens 6 12 \
    --n_trials 200
```

## ⚠️ 注意事项

1. **资源需求**: 每次试验需要训练完整模型，建议在GPU环境下运行
2. **时间估算**: 100次试验×3个预测长度 ≈ 数小时到一天
3. **内存管理**: 脚本会自动清理GPU内存
4. **环境要求**: 确保LIFT conda环境已正确配置
5. **中断恢复**: 可随时Ctrl+C中断，部分结果会自动保存

## 🐛 故障排除

### 常见问题

1. **依赖包缺失**
   ```bash
   pip install -r hpo_requirements_basic.txt
   ```

2. **conda环境问题**
   ```bash
   conda env list  # 检查环境是否存在
   conda activate LIFT  # 手动激活测试
   ```

3. **RMSE解析失败**
   - 检查run_longExp.py输出格式
   - 查看日志文件中的详细错误信息

4. **可视化问题**
   ```bash
   pip install matplotlib seaborn pandas
   ```

### 调试模式
在脚本开头添加：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 性能优化建议

1. **并行化**: 考虑使用Optuna的分布式优化
2. **早停策略**: 使用Optuna的pruning功能
3. **采样策略**: 尝试不同的sampler（TPE, CMA-ES等）
4. **资源分配**: 根据硬件配置调整试验次数

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

本项目遵循MIT许可证。
