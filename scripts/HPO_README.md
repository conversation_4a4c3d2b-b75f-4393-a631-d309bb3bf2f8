# TimeXer 超参数优化 (HPO) 工具

本工具集提供了使用 Optuna 和 Ray Tune 两种框架对 TimeXer 模型进行超参数优化的完整解决方案。

## 功能特性

- 🔍 **多参数同时优化**: 同时搜索 `d_model`、`d_ff`、`patch_len` 三个关键参数
- 🚀 **多种优化框架**: 支持 Optuna 和 Ray Tune 两种主流HPO框架
- 📊 **智能搜索策略**: 使用高效的搜索算法和早停策略
- 📈 **结果可视化**: 提供详细的分析报告和可视化图表
- 🔧 **易于使用**: 简单的命令行界面和配置选项

## 安装依赖

```bash
pip install -r scripts/hpo_requirements.txt
```

## 快速开始

### 1. 使用启动脚本（推荐）

```bash
# 使用Optuna优化guojia数据集
./scripts/run_hpo.sh --dataset guojia --method optuna --n_trials 50

# 使用Ray Tune优化lianghe数据集
./scripts/run_hpo.sh --dataset lianghe --method ray_tune --n_trials 100
```

### 2. 直接运行Python脚本

#### 使用Optuna

```bash
python scripts/optuna_hpo.py \
    --dataset guojia \
    --pred_len 12 \
    --n_trials 50
```

#### 使用Ray Tune

```bash
python scripts/ray_tune_hpo.py \
    --dataset guojia \
    --pred_len 12 \
    --num_samples 50 \
    --max_concurrent 4
```

## 参数说明

### 通用参数

- `--dataset`: 数据集名称，支持 `guojia`, `lianghe`, `chengkou`, `yantang`
- `--pred_len`: 预测长度，默认为 12
- `--n_trials` / `--num_samples`: 优化试验次数，默认为 50

### Optuna特有参数

- `--study_name`: 研究名称，用于标识优化任务
- `--storage`: 数据库存储路径，支持持久化存储

### Ray Tune特有参数

- `--max_concurrent`: 最大并发试验数，默认为 4

## 搜索空间

当前配置的搜索空间如下：

- **d_model**: [16, 32, 64, 128, 256, 512, 1024]
- **d_ff**: d_model × [1, 2, 4, 8]
- **patch_len**: [4, 8, 16, 24, 32] (自动确保能整除seq_len)

## 结果分析

### 自动生成的结果

优化完成后，会在相应目录下生成：

- `best_result.json`: 最佳参数配置
- `trial_*.json`: 每次试验的详细结果（仅Optuna）

### 使用分析脚本

```bash
python scripts/analyze_hpo_results.py \
    --results_dir optuna_results/your_study_name \
    --output_dir analysis_output
```

分析脚本会生成：

- 📊 参数分布图
- 📈 优化历史图  
- 🔥 参数热力图
- 📋 详细统计报告
- 💾 最佳配置文件

## 输出示例

```
=== 基本统计信息 ===
总试验次数: 50
最佳RMSE: 0.123456
最差RMSE: 0.987654
平均RMSE: 0.456789

=== Top 10 最佳配置 ===
第 1 名:
  RMSE: 0.123456
  d_model: 256
  d_ff: 1024
  patch_len: 8
```

## 高级用法

### 1. 持久化存储（Optuna）

```bash
python scripts/optuna_hpo.py \
    --dataset guojia \
    --storage sqlite:///optuna_study.db \
    --study_name my_optimization
```

### 2. 分布式优化（Ray Tune）

```bash
# 在集群环境中运行
ray start --head
python scripts/ray_tune_hpo.py --dataset guojia --max_concurrent 8
```

### 3. 自定义搜索空间

编辑脚本中的 `define_search_space` 方法来自定义搜索范围。

## 注意事项

1. **资源需求**: 每次试验需要训练一个完整的模型，建议在GPU环境下运行
2. **时间估算**: 50次试验大约需要数小时到一天时间，具体取决于硬件配置
3. **内存管理**: 脚本会自动清理GPU内存，避免内存溢出
4. **结果保存**: 所有结果都会自动保存，可以随时中断和恢复

## 故障排除

### 常见问题

1. **RMSE解析失败**: 检查 `run_longExp.py` 的输出格式是否发生变化
2. **内存不足**: 减少 `max_concurrent` 参数或使用更小的模型
3. **依赖包冲突**: 使用虚拟环境安装依赖

### 调试模式

在脚本中设置 `logging.basicConfig(level=logging.DEBUG)` 获取详细日志。

## 扩展功能

- 支持更多超参数（学习率、批次大小等）
- 集成更多优化算法（贝叶斯优化、遗传算法等）
- 支持多目标优化（RMSE + 训练时间）
- 自动化模型部署

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
